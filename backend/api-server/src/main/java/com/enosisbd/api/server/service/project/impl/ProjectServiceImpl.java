package com.enosisbd.api.server.service.project.impl;

import com.enosisbd.api.server.dto.*;
import com.enosisbd.api.server.entity.Project;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.ProjectRepository;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import com.enosisbd.api.server.service.googlesheets.GoogleSheetsService;
import com.enosisbd.api.server.service.module.ModuleService;
import com.enosisbd.api.server.service.project.ProjectService;
import com.enosisbd.api.server.service.submodule.SubModuleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.*;

/**
 * The type Project service.
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class ProjectServiceImpl implements ProjectService {

    private final ProjectRepository repository;
    private final EntitySharingService entitySharingService;
    private final AuthorizationService authorizationService;
    private final GoogleSheetsService googleSheetsService;
    private final ModuleService moduleService;
    private final SubModuleService subModuleService;

    /**
     * Find all Project list.
     *
     * @return the project list
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<ProjectDto> findAll() {
        List<Project> accessibleProjects =
                authorizationService.isCurrentUserAdmin()
                        ? repository.findAllByOrderByUpdatedAtDesc()
                        : repository.findBySharedWithUserOrderByUpdatedAtDesc(
                        authorizationService.getCurrentUsername(),
                        authorizationService.getCurrentUserId());

        // Convert to DTOs
        return accessibleProjects.stream()
                .map(this::convertToDto)
                .toList();
    }

    /**
     * Exists by project id boolean.
     *
     * @param id the id
     * @return the boolean
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public boolean existsById(Long id) {
        Optional<Project> project = repository.findById(id);
        if (project.isEmpty()) {
            return false;
        }

        return entitySharingService.hasAccess(project.get(), EntityType.PROJECT);
    }

    /**
     * Gets project by id.
     *
     * @param id the id
     * @return the by id
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<ProjectDto> getById(Long id) {
        Optional<Project> project = repository.findById(id);

        if (project.isEmpty()) {
            return Optional.empty();
        }

        return project.map(this::convertToDto);
    }

    /**
     * Add project with validation and auto-correction for submodule names
     *
     * @param dto the project dto
     * @return the project processing result with validation issues
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public ProjectProcessingResultDto add(ProjectDto dto) {
        // Save the project with the Google Sheet ID if available
        Project entity = convertToEntity(dto);
        repository.save(entity);
        ProjectDto savedDto = convertToDto(entity);

        // Process Google Sheet data if URL is provided
        if (StringUtils.hasText(dto.getGoogleSheetUrl())) {
            try {
                // Process Google Sheet with validation and update the project with modules and submodules
                ProjectProcessingResultDto result = processGoogleSheet(savedDto);

                // Refresh the DTO with the updated entity data
                Optional<Project> updatedProject = repository.findById(entity.getId());
                if (updatedProject.isPresent()) {
                    ProjectDto refreshedDto = convertToDto(updatedProject.get());
                    result.setProject(refreshedDto);
                }

                return result;
            } catch (Exception e) {
                log.error("Error processing Google Sheet: {}", e.getMessage(), e);
                throw new BadRequestRestException("Error processing Google Sheet: " + e.getMessage());
            }
        }

        // If no Google Sheet URL, return result without validation issues
        return ProjectProcessingResultDto.builder()
                .project(savedDto)
                .validationIssues(new ArrayList<>())
                .hasValidationIssues(false)
                .userGuidanceMessage(null)
                .build();
    }

    /**
     * Process Google Sheet data with validation and auto-correction
     *
     * @param projectDto The project DTO with Google Sheet information
     * @return ProjectProcessingResultDto containing the project and validation issues
     * @throws IOException If there is an error accessing the Google Sheet
     * @throws GeneralSecurityException If there is a security error
     */
    private ProjectProcessingResultDto processGoogleSheet(ProjectDto projectDto) throws IOException, GeneralSecurityException {
        log.info("Processing Google Sheet with validation for project {}: {}", projectDto.getId(), projectDto.getGoogleSheetUrl());

        // Use the sheet ID and user email that were already set during project creation
        String sheetId = projectDto.getGoogleSheetId();
        String userEmail = authorizationService.getCurrentUsername();

        if (sheetId == null) {
            // If somehow the sheet ID is not set, extract it from the URL
            sheetId = googleSheetsService.extractSheetId(projectDto.getGoogleSheetUrl());
            projectDto.setGoogleSheetId(sheetId);
        }

        log.info("Using Google Sheet ID: {} and current user: {}", sheetId, userEmail);

        // Use optimized batch API to get filtered sheets data in a single request
        log.info("Retrieving filtered sheets data using optimized batch API for optimal performance");
        SpreadsheetBatchResponseDto batchResponse = googleSheetsService.getAllSheetsDataBatch(sheetId, userEmail, projectDto.getExcludedTabs());

        if (batchResponse.hasErrors()) {
            log.warn("Batch retrieval had errors: {}", batchResponse.getErrorMessages());
        }

        if (batchResponse.getSheets().isEmpty()) {
            throw new BadRequestRestException("No sheets found in the Google Sheet or all sheets were excluded");
        }

        log.info("Successfully retrieved {} filtered sheets using optimized batch API (out of {} total sheets)",
                batchResponse.getSheets().size(), batchResponse.getTotalSheets());

        // Convert column letter to index (0-based)
        int columnIndex = googleSheetsService.columnLetterToIndex(projectDto.getSubmoduleColumn());

        // Convert start row to 0-based index (subtract 1)
        int startRowIndex = projectDto.getSubmoduleStartRow() - 1;
        if (startRowIndex < 0) {
            startRowIndex = 0;
        }

        // Collect all validation issues across all sheets
        List<SubmoduleValidationIssueDto> allValidationIssues = new ArrayList<>();

        // Process each sheet as a module
        for (SheetBatchDataDto sheetBatchData : batchResponse.getSheets()) {
            String sheetName = sheetBatchData.getSheetName();

            if (!sheetBatchData.isDataRetrieved()) {
                log.warn("Skipping sheet '{}' due to data retrieval error: {}", sheetName, sheetBatchData.getErrorMessage());
                continue;
            }

            // Create module
            ModuleDto moduleDto = new ModuleDto();
            moduleDto.setName(sheetName);
            moduleDto.setProjectId(projectDto.getId());

            moduleDto = moduleService.add(moduleDto);

            // Get sheet data from batch response (no additional API call needed!)
            List<List<Object>> sheetData = sheetBatchData.getData();

            // Extract submodules with validation
            SubmoduleExtractionResultDto extractionResult = googleSheetsService.extractSubmodulesWithValidation(
                    sheetData, columnIndex, startRowIndex, sheetName, projectDto.getSubmoduleColumn());

            // Add validation issues from this sheet to the overall list
            if (extractionResult.hasValidationIssues()) {
                allValidationIssues.addAll(extractionResult.getValidationIssues());
            }

            // Create submodules (using the corrected names)
            for (SubModuleRangeDto submoduleRange : extractionResult.getSubmodules()) {
                SubModuleDto subModuleDto = new SubModuleDto();
                subModuleDto.setName(submoduleRange.getName());
                subModuleDto.setModuleId(moduleDto.getId());
                subModuleDto.setStartRow(submoduleRange.getStartRow());
                subModuleDto.setEndRow(submoduleRange.getEndRow());

                subModuleService.add(subModuleDto);
            }
        }

        // Make sure the project entity has the Google Sheet ID
        Optional<Project> projectOpt = repository.findById(projectDto.getId());
        if (projectOpt.isPresent()) {
            Project project = projectOpt.get();
            if (!Objects.equals(project.getGoogleSheetId(), sheetId)) {
                log.info("Updating project with Google Sheet ID");
                project.setGoogleSheetId(sheetId);
                repository.save(project);

                // Update the DTO with the saved entity data
                convertToDto(projectDto, project);
            }
        }

        // Generate user guidance message if there are validation issues
        String userGuidanceMessage = null;
        if (!allValidationIssues.isEmpty()) {
            userGuidanceMessage = generateUserGuidanceMessage(allValidationIssues);
        }

        log.info("Finished processing Google Sheet for project {} with {} validation issues",
                projectDto.getId(), allValidationIssues.size());

        return ProjectProcessingResultDto.builder()
                .project(projectDto)
                .validationIssues(allValidationIssues)
                .hasValidationIssues(!allValidationIssues.isEmpty())
                .userGuidanceMessage(userGuidanceMessage)
                .build();
    }

    /**
     * Generate user guidance message for validation issues
     *
     * @param validationIssues List of validation issues
     * @return User-friendly guidance message
     */
    private String generateUserGuidanceMessage(List<SubmoduleValidationIssueDto> validationIssues) {
        if (validationIssues.isEmpty()) {
            return null;
        }

        // Count different types of issues
        long emptyCellIssues = validationIssues.stream()
                .filter(issue -> "EMPTY_CELL".equals(issue.getIssueType()))
                .count();
        long shortNameIssues = validationIssues.stream()
                .filter(issue -> "NAME_TOO_SHORT".equals(issue.getIssueType()))
                .count();

        StringBuilder message = new StringBuilder();
        message.append("The following submodule names were auto-corrected:\n\n");

        if (emptyCellIssues > 0) {
            message.append("• ").append(emptyCellIssues)
                   .append(" empty submodule cell(s) (including merged cells) were detected and auto-corrected.\n");
        }

        if (shortNameIssues > 0) {
            message.append("• ").append(shortNameIssues)
                   .append(" submodule name(s) were too short (minimum 3 characters) and auto-corrected.\n");
        }

        message.append("\nUpdate them in your Google Sheet and refresh the project.");
        return message.toString();
    }

    /**
     * Update project with validation and auto-correction for submodule names
     *
     * @param dto the dto
     * @return the project processing result with validation issues
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public ProjectProcessingResultDto update(ProjectDto dto) {
        var maybe = repository.findById(dto.getId());
        if (maybe.isEmpty()) {
            throw new BadRequestRestException("Project not found with ID: " + dto.getId());
        }

        Project existing = maybe.get();
        // Check if Google Sheet related fields have changed
        boolean googleSheetFieldsChanged =
            !Objects.equals(existing.getGoogleSheetUrl(), dto.getGoogleSheetUrl()) ||
            !Objects.equals(existing.getSubmoduleColumn(), dto.getSubmoduleColumn()) ||
            !Objects.equals(existing.getSubmoduleStartRow(), dto.getSubmoduleStartRow()) ||
            !Objects.equals(existing.getExcludedTabs(), dto.getExcludedTabs());

        // Update the project

        Project entity = convertToEntity(existing, dto);
        repository.save(entity);

        // If Google Sheet fields changed and URL is provided, refresh from Google Sheet with validation
        if (googleSheetFieldsChanged && StringUtils.hasText(dto.getGoogleSheetUrl())) {
            try {
                log.info("Google Sheet fields changed, refreshing data from Google Sheet with validation");
                Optional<ProjectProcessingResultDto> refreshResult = refreshFromGoogleSheet(entity.getId());

                if (refreshResult.isPresent()) {
                    return refreshResult.get();
                }
            } catch (Exception e) {
                log.error("Error refreshing Google Sheet data after update: {}", e.getMessage(), e);
                throw new BadRequestRestException("Error refreshing Google Sheet data: " + e.getMessage());
            }
        }

        // If no Google Sheet changes, return result without validation issues
        return ProjectProcessingResultDto.builder()
                .project(convertToDto(entity))
                .validationIssues(new ArrayList<>())
                .hasValidationIssues(false)
                .userGuidanceMessage(null)
                .build();
    }

    /**
     * Delete optional.
     *
     * @param id the id
     * @return the optional
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<Boolean> delete(Long id) {
        Optional<Project> project = repository.findById(id);
        if (project.isEmpty()) {
            return Optional.empty();
        }

        // Delete the project
        repository.delete(project.get());
        return Optional.of(true);
    }


    private ProjectDto convertToDto(Project entity) {
        return convertToDto(new ProjectDto(), entity);
    }

    /**
     * Refresh project data from Google Sheet with validation
     *
     * @param id the project id
     * @return the project processing result with validation issues
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<ProjectProcessingResultDto> refreshFromGoogleSheet(Long id) {
        // Find the project
        Optional<Project> projectOpt = repository.findById(id);
        if (projectOpt.isEmpty()) {
            return Optional.empty();
        }

        Project project = projectOpt.get();

        // Check if the project has Google Sheet URL
        if (!StringUtils.hasText(project.getGoogleSheetUrl())) {
            throw new BadRequestRestException("Project does not have a Google Sheet URL");
        }

        // Convert to DTO
        ProjectDto projectDto = convertToDto(project);

        try {
            // Delete existing modules and submodules
            moduleService.deleteByProjectId(id);

            // Process Google Sheet with validation and create new modules and submodules
            ProjectProcessingResultDto result = processGoogleSheet(projectDto);

            // Refresh the project data in the result
            projectOpt = repository.findById(id);
            if (projectOpt.isPresent()) {
                result.setProject(convertToDto(projectOpt.get()));
            }

            return Optional.of(result);
        } catch (Exception e) {
            log.error("Error refreshing Google Sheet data: {}", e.getMessage(), e);
            throw new BadRequestRestException("Error refreshing Google Sheet data: " + e.getMessage());
        }
    }

    private ProjectDto convertToDto(ProjectDto dto, Project entity) {
        dto.setId(entity.getId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setCreatedBy(entity.getCreatedBy());

        // Set Google Sheet metadata
        dto.setGoogleSheetId(entity.getGoogleSheetId());
        dto.setGoogleSheetUrl(entity.getGoogleSheetUrl());
        dto.setSubmoduleColumn(entity.getSubmoduleColumn());
        dto.setSubmoduleStartRow(entity.getSubmoduleStartRow());
        dto.setCaseIdColumn(entity.getCaseIdColumn());
        dto.setTestCaseDescriptionColumn(entity.getTestCaseDescriptionColumn());
        dto.setTestCaseExpectedResultColumn(entity.getTestCaseExpectedResultColumn());
        dto.setExcludedTabs(entity.getExcludedTabs());
        return dto;
    }

    private Project convertToEntity(ProjectDto dto) {
        return convertToEntity(new Project(), dto);
    }

    private Project convertToEntity(Project entity, ProjectDto dto) {
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setDescription(dto.getDescription());

        // Set Google Sheet metadata if available
        if (dto.getGoogleSheetUrl() != null) {
            entity.setGoogleSheetUrl(dto.getGoogleSheetUrl());
            if (dto.getGoogleSheetId() != null) {
                entity.setGoogleSheetId(dto.getGoogleSheetId());
            } else {
                entity.setGoogleSheetId(googleSheetsService.extractSheetId(dto.getGoogleSheetUrl()));
            }
        }
        if (dto.getSubmoduleColumn() != null) {
            entity.setSubmoduleColumn(dto.getSubmoduleColumn());
        }
        if (dto.getSubmoduleStartRow() != null) {
            entity.setSubmoduleStartRow(dto.getSubmoduleStartRow());
        }
        if (dto.getCaseIdColumn() != null) {
            entity.setCaseIdColumn(dto.getCaseIdColumn());
        }
        if (dto.getTestCaseDescriptionColumn() != null) {
            entity.setTestCaseDescriptionColumn(dto.getTestCaseDescriptionColumn());
        }
        if (dto.getTestCaseExpectedResultColumn() != null) {
            entity.setTestCaseExpectedResultColumn(dto.getTestCaseExpectedResultColumn());
        }
        if (dto.getExcludedTabs() != null) {
            entity.setExcludedTabs(dto.getExcludedTabs());
        }

        return entity;
    }
}
